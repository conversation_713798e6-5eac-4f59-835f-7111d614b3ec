name: "Build/Push container"
description: "Build a BCC CI container and push it when not a pull-request."

inputs:
  os_distro:
    description: "OS Disctribution. Ex: ubuntu"
    required: true
  os_version:
    description: "Version of the OS. Ex: 24.04"
    required: true
  os_nick:
    description: "Nickname of the OS. Ex: noble"
    required: true
  llvm_versions:
    description: "Space separated list of llvm versions to install in the container. Only supported for Ubuntu containers."
    type: string
    default: "15"
  registry:
    description: "Registry where to push images"
    default: ghcr.io
  password:
    description: "Password used to log into the docker registry."
  push:
    description: "Whether or not to push the build image"
    type: boolean
    default: false

runs:
  using: "composite"
  steps:
    # Login against registry except on PR
    # https://github.com/docker/login-action
    - name: Log into registry ${{ inputs.registry }}
      if: ${{ inputs.push == 'true' && github.event_name != 'pull_request' }}

      uses: docker/login-action@v2
      with:
        registry: ${{ inputs.registry }}
        username: ${{ github.actor }}
        password: ${{ inputs.password }}

    - name: Build and push
      uses: docker/build-push-action@v3
      with:
        push: ${{ inputs.push == 'true' && github.event_name != 'pull_request' }}
        build-args: |
          VERSION=${{ inputs.os_version }}
          SHORTNAME=${{ inputs.os_nick }}
          LLVM_VERSION=${{ inputs.llvm_versions }}
        file: docker/build/Dockerfile.${{ inputs.os_distro }}
        tags: ${{ inputs.registry }}/${{ github.repository }}:${{ inputs.os_distro }}-${{ inputs.os_version }}

