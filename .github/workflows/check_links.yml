name: Check Broken Links

on:
  schedule:
    # First day of month at 00:00 in every 2nd month
    - cron: '0 0 1 */2 *'
  workflow_dispatch:     

jobs:
  check-links:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests
          
      - name: Check links in LINKS.md
        id: link-check
        run: python .github/scripts/check_links.py
        
      - name: Create issue for broken links
        if: steps.link-check.outputs.broken_links == 'true'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const report = `${{ steps.link-check.outputs.report }}`;
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: 'Broken links detected in LINKS.md',
              body: report,
              labels: ['documentation']
            });