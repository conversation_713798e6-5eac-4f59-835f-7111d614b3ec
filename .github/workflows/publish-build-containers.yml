name: Publish Build Containers

on:
  # We want to update this image regularly and when updating master
  schedule:
    - cron: '00 18 * * *'
  push:
    branches:
      - master
  pull_request:
    paths:
      - 'docker/build/**'

permissions: {}

jobs:

  publish_ghcr:
    permissions:
      contents: read # to fetch code (actions/checkout)
      packages: write # to push container
    name: Publish To GitHub Container Registry
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [
          {distro: "ubuntu", version: "24.04", nick: noble, installed_llvm_versions: "15 17 19"},
          {distro: "fedora", version: "38", nick: "f38", installed_llvm_versions: "this is not used"},
        ]

    steps:

    - uses: actions/checkout@v4

    - name: Build and push
      uses: ./.github/actions/build-container
      with:
        os_distro: ${{ matrix.os.distro }}
        os_version: ${{ matrix.os.version }}
        os_nick: ${{ matrix.os.nick }}
        llvm_versions: ${{ matrix.os.installed_llvm_versions }}
        password: ${{ secrets.GITHUB_TOKEN }}
        push: true
