ARG OS_TAG=22.04
FROM ubuntu:${OS_TAG} as builder

ARG OS_TAG
ARG BUILD_TYPE=release
ARG DEBIAN_FRONTEND=noninteractive

MAINTAINER Brenden Blanco <<EMAIL>>

RUN apt-get -qq update && \
    apt-get -y install pbuilder aptitude

COPY ./ /root/bcc

WORKDIR /root/bcc

RUN /usr/lib/pbuilder/pbuilder-satisfydepends && \
    ./scripts/build-deb.sh ${BUILD_TYPE}

FROM ubuntu:${OS_TAG}

COPY --from=builder /root/bcc/*.deb /root/bcc/

RUN \
  apt-get update -y && \
  DEBIAN_FRONTEND=noninteractive apt-get install -y python3 python3-pip python-is-python3 binutils libelf1 kmod llvm-12-dev && \
  pip3 install dnslib cachetools pyelftools  && \
  dpkg -i /root/bcc/*.deb && \
  apt-get clean
