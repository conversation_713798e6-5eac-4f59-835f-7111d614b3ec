# Find the kernel headers for the running kernel release
# This is used to find a "linux/version.h" matching the running kernel.

execute_process(
        COMMAND uname -r
        OUTPUT_VARIABLE KERNEL_RELEASE
        OUTPUT_STRIP_TRAILING_WHITESPACE
)

# Find the headers
find_path(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>DERS_DIR
        include/linux/user.h
        PATHS
        # RedHat derivatives
        /usr/src/kernels/${KERNEL_RELEASE}
        # Debian derivatives
        /usr/src/linux-headers-${KERNEL_RELEASE}
        )

message(STATUS "Kernel release: ${KERNEL_RELEASE}")
message(STATUS "Kernel headers: ${KERNELHEADERS_DIR}")

if (<PERSON><PERSON><PERSON><PERSON>H<PERSON>DERS_DIR)
    set(<PERSON><PERSON><PERSON>LHEADERS_INCLUDE_DIRS
            ${KERNELHEADERS_DIR}/include/generated/uapi
            CACHE PATH "Kernel headers include dirs"
            )
    set(KERNELHEADERS_FOUND 1 CACHE STRING "Set to 1 if kernel headers were found")
    include_directories(${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIRS})
else (<PERSON>ERNELHEADERS_DIR)
    set(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>DERS_FOUND 0 CACHE STRING "Set to 1 if kernel headers were found")
endif (KERNELHEADERS_DIR)

mark_as_advanced(KERNELHEADERS_FOUND)

