bcc (0.35.0-1) unstable; urgency=low

  * Support for kernel up to 6.14
  * New bcc tools: mptcp: enable mptcp for tcp traffic
  * tools/biosnoop: Fix biosnoop pattern option
  * Allow cmake run out of the source tree
  * Fix for test bpf_stack_id when running on custom image in yocto
  * libbpf-tools/map_helpers: Add bpf_map_lookup_and_delete_batch to dump_hash
  * libbpf-tools/biotop: Use dump_hash for map processing
  * uninstall: use execute_process() instead of exec_program()
  * clang: Fix pointer dereference on big-endian machines
  * ci: Upgrade to ubuntu 24.04
  * doc update, other bug fixes and example improvement.

bcc (0.34.0-1) unstable; urgency=low

  * Support for kernel up to 6.13
  * Bump cmake minimum version to 3.12
  * statsnoop: Display syscall name with -s
  * readahead: Fix incorrect page accessed count since kernel 5.16
  * libbpf-tools/opensnoop: Add new fields
  * libbpf-tools: hardirqs/softirqs: Fix logarithmic calculation issue
  * libbpf-tools/hardirqs: have better default display and add CPU column
  * libbpf-tools/klockstat: Better stack dump and summary info
  * libbpf-tools/sigsnoop: Support real-time signals and thread comm
  * libbpf-tools/statsnoop: Support more syscalls
  * libbpf-tools/memleak: Some fixes and better messages
  * tools/opensnoop: Add new fields and fix bad mode value
  * tools/profile: Prioritize using the cpu-cycles hardware event
  * tools/tcpdrop: Add support for dumping TCP drop reasons
  * Fix event name too long error in python source
  * doc update, other bug fixes and example improvement.

bcc (0.33.0-1) unstable; urgency=low

  * Support for kernel up to 6.12
  * Add new bcc tool numasched
  * syms: Initialize ModulePath::fd_ to invalid FD
  * libbpf-tools/memleak: Fix off-by-one error
  * libbpf-tools/slabratetop: Fix failed to create kprobe error
  * libbpf_tools/profile: Support PID namespace mapping
  * libbpf-tools/mountsnoop: Support fsopen,fsconfig,fsmount,move_mount syscalls
  * tools/oomkill: get application level stack trace
  * tools/profile: Add additional information to backtrace
  * tools/mountsnoop: Fix fsmount printing wrong flags
  * tools/compactsnoop: Add aarch64 support
  * doc update, other bug fixes and tools improvement.

bcc (0.32.0-1) unstable; urgency=low

  * Support for kernel up to 6.11.
  * bcc tool update: wakeuptime, readahead, shmsnoop, offcputime, cachestat, cachetop, hardirqs
  * libbpf tool update: futexctn, profile, readhead, softirqs, hardirqs
  * Multiple enhancements for memleak, better error path checking, adding mremap uprobe
  * Support get pid/tgid in pid namespaces (cpudist, profile)
  * multiple pid filtering support: profile, offcputime
  * detect whether elf binary is PIE even if the binary is marked as DYN
  * Fix several compilation issues with llvm20
  * doc update, other bug fixes and tools improvement.

bcc (0.31.0-1) unstable; urgency=low

  * Support for kernel up to 6.9.
  * Add support for bcachefs to fsdist and fsslower tools
  * libbpf tool update: memleak, syncsnoop, numamove, syscount, vfsstat, tcptop, capable, syncsnoop, sigsnoop, etc.
  * bcc tool update: biolatency, biosnoop, biotop, vfsstat, kvmexit, sslsniff, swapin, etc.
  * build: Remove llvm-dev dependency from libbcc
  * build: Remove dependency on LLVM header from libbcc packages
  * usdt: Fix bare register dereference on aarch64
  * Extend `bcc_proc` API which allows to limit search to specific pid
  * Fix several flaky tests.
  * doc update, other bug fixes and tools improvement.

 -- Yonghong Song <<EMAIL>>  Sat, 27 Jul 2024 17:00:00 +0000

bcc (0.30.0-1) unstable; urgency=low

  * Support for kernel up to 6.8.
  * Set minimum supported llvm version to 12, and add llvm17 test.
  * Add workqueue latency observation tool.
  * libbpf tool update: f2fsslower, opensnoop, futexctn, bindsnoop, ksnoop, klockstat, offcputime, etc.
  * bcc tool update: memleak, ttysnoop, bashreadline, tcpdrop, execsnoop, etc.
  * allow more flexible perf event options with new perf_custom_event_open() python API.
  * Fix userspace stack unwinding on powerpc.
  * add bpf_prog_test_run_opts() python API.
  * several deb package related changes.
  * Fix btf_type_tag issue with llvm 15.
  * Fix several flaky tests.
  * classify tools into different sub-categories.
  * doc update, other bug fixes and tools improvement.

 -- Yonghong Song <<EMAIL>>  Thu, 24 Mar 2024 17:00:00 +0000

bcc (0.29.1-1) unstable; urgency=low

  * Fix Ubuntu 22.04 deb build
  * Fix Ubuntu 22.04 Docker image build

 -- Adrian Vladu <<EMAIL>>  Thu, 14 Dec 2023 17:00:00 +0000

bcc (0.29.0-1) unstable; urgency=low

  * Support for kernel up to 6.6.
  * new bcc tools: rdmaucma
  * new libbpf tools: f2fs, futexctn
  * bcc tool update: tcpstates, statsnoop, runqlat, bio tools, tcptop, slabratetop, tcprtt, etc.
  * libbpf tool update: tcprtt, tcppktlat, bio tools, execsnoop, bindsnoop, exitsnoop, etc.
  * s390x support for libbpf-tools
  * examples for perf/ipc
  * expose pid parameter in bpf_open_perf_event
  * allow for installing python as a non-system package
  * ci improvement: deprecate ubuntu 18.04 and allow multiple llvm versions
  * ci improvement: reitre fedora 34/26 and use fedora 38.
  * fix misaligned pointer accesses in some ringbuf using libbpf-tools
  * some new  enhancement for powerpc and riscv.
  * consolidate tools into different categories: filesystem/storage, networking, cpu and scheduling, etc.
  * doc update, other bug fixes and tools improvement

 -- Yonghong Song <<EMAIL>>  Thu, 6 Dec 2023 17:00:00 +0000

bcc (0.28.0-1) unstable; urgency=low

  * Support for kernel up to 6.3.
  * new libbpf tool: tcppktlat.
  * bcc tool updates: funcslower, wakeuptime, profile, offcputime, deadlock, funccount, argdist, kvmexit, runqlen and cpuunclaimed.
  * libbpf tool update: memleak, tcprtt, tcpconnlat, funclatency, syscount, cpufreq, biosnoop.
  * support ringbuf_query for bcc tools.
  * handle '[uprobes]' memory mapped file properly during stack tracing.
  * Fix maximum allowed index for print_linear_hist for bcc tools.
  * add module kfunc/kretfunc support.
  * clang rewriter: initialize only the requested parameters
  * filter with available_filter_functions to make multi-functions kprobes more robust for both bcc and libbpf tools.
  * doc update, other bug fixes and tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 28 Jun 2023 17:00:00 +0000

bcc (0.27.0-1) unstable; urgency=low

  * Support for kernel up to 6.2
  * bcc tool updates for ttysnoop, slabratetop, readahead, nfsslower, cpudist, cachetop, cachestat, etc.
  * libbpf-tools updates for mdflush, drsnoop, statsnoop, ttysnoop, softirqs, wakeuptime, cachestat, numamove, etc.
  * fix for incomplete static libraries
  * implement zip archive support
  * upgrade to use c++14 standard
  * new libbpf-tools: memleak
  * add loongarch support in libbpft-tools
  * doc update, bug fixes and other tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 02 Apr 2023 17:00:00 +0000

bcc (0.26.0-1) unstable; urgency=low

  * Support for kernel up to 6.1
  * bcc tool updates for biosnoop, opensnoop, biopattern, killsnoop, runqslower, offcputime, wakeuptime, etc.
  * libbpf-tools updates for klockstat, sigsnoop, hardirqs, softirqs, opensnoop, statsnoop, offcputime, tcplife, cpufreq, cpudist, etc.
  * new libbpf-tools: tcptop, tcpstates, biotop, capable
  * ci: add support for fedora 36 container and new workflow for containers
  * doc update, bug fixes and other tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 10 Aug 2022 17:00:00 +0000

bcc (0.25.0-1) unstable; urgency=low

  * Support for kernel up to 5.19
  * bcc tool updates for oomkill.py, biolatpcts.py, sslsniff.py, tcpaccept.py, etc.
  * libbpf tool updates for klockstat, opensnoop, tcpconnect, etc.
  * new bcc tools: tcpcong
  * new libbpf tools: tcpsynbl, mdflush, oomkill, sigsnoop
  * usdt: support xmm registers as args for x64
  * bpftool as a submodule now
  * remove uses of libbpf deprecated APIs
  * use new llvm pass manager
  * support cgroup filtering libbpf tools
  * fix shared lib module offset <-> global addr conversion
  * riscv support
  * LoongArch support
  * doc update, bug fixes and other tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 10 Aug 2022 17:00:00 +0000

bcc (0.24.0-1) unstable; urgency=low

  * Support for kernel up to 5.16
  * bcc tools: update for trace.py, sslsniff.py, tcptop.py, hardirqs.py, etc.
  * new libbpf tools: bashreadline
  * allow specify wakeup_events for perf buffer
  * support BPF_MAP_TYPE_{INODE, TASK}_STORAGE maps
  * remove all deprecated libbpf function usage
  * remove P4/B language support
  * major test infra change, using github actions now
  * doc update, bug fixes and other tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 14 Jan 2022 17:00:00 +0000

bcc (0.23.0-1) unstable; urgency=low

  * Support for kernel up to 5.15
  * bcc tools: update for kvmexit.py, tcpv4connect.py, cachetop.py, cachestat.py, etc.
  * libbpf tools: update for update for mountsnoop, ksnoop, gethostlatency, etc.
  * fix renaming of task_struct->state
  * get pid namespace properly for a number of tools
  * initial work for more libbpf utilization (less section names)
  * doc update, bug fixes and other tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 15 Nov 2021 17:00:00 +0000

bcc (0.22.0-1) unstable; urgency=low

  * Support for kernel up to 5.14
  * add ipv4/ipv6 filter support for tcp trace tools
  * add python interface to attach raw perf events
  * fix tcpstates for incorrect display of dport
  * new options for bcc tools runqslower, argdist
  * new libbpf-tools: filetop, exitsnoop, tcprtt
  * doc update, bug fixes and other tools improvement

 -- Yonghong Song <<EMAIL>>  Wed, 15 Sep 2021 17:00:00 +0000

bcc (0.21.0-1) unstable; urgency=low

  * Support for kernel up to 5.13
  * support for debug information from libdebuginfod
  * finished support for map elements items_*_batch() APIs
  * add atomic_increment() API
  * support attach_func() and detach_func() in python
  * fix displaying PID instead of TID for many tools
  * new tools: kvmexit.py
  * new libbpf-tools: gethostlatency, statsnoop, fsdist and solisten
  * fix tools ttysnoop/readahead for newer kernels
  * doc update and bug fixes

 -- Yonghong Song <<EMAIL>>  Mon, 16 Jul 2021 17:00:00 +0000

bcc (0.20.0-1) unstable; urgency=low

  * Support for kernel up to 5.12
  * Some basic support for MIPS
  * added bpf_map_lookup_batch and bpf_map_delete_batch support
  * tools/funclatency.py support nested or recursive functions
  * tools/biolatency.py can optionally print out average/total value
  * fix possible marco HAVE_BUILTIN_BSWAP redefine warning for kernel >= 5.10.
  * new tools: virtiostat
  * new libbpf-tools: ext4dist
  * doc update and bug fixes

 -- Yonghong Song <<EMAIL>>  Mon, 5 May 2021 17:00:00 +0000

bcc (0.19.0-1) unstable; urgency=low

  * Support for kernel up to 5.11
  * allow BCC as a cmake subproject
  * add LPORT support in tcpconnlat and tcpconnect
  * added bpf_map_lookup_and_delete_batch support
  * new tools: virtiostat
  * new libbpf-tools: cpufreq, funclatency, cachestat
  * add install target to libbpf-tools
  * a few lua fixes
  * doc update and bug fixes

 -- Yonghong Song <<EMAIL>>  Mon, 19 Mar 2021 17:00:00 +0000

bcc (0.18.0-1) unstable; urgency=low

  * Support for kernel up to 5.10
  * add bpf kfunc/kretfunc C++ example
  * add PT_REGS_PARMx_SYSCALL helper macro
  * biolatency: allow json output
  * biolatpcts: support measuring overall latencies between two events
  * fix build when ENABLE_CLANG_JIT is disabled
  * doc update and bug fixes

 -- Yonghong Song <<EMAIL>>  Mon, 4 Jan 2021 17:00:00 +0000

bcc (0.17.0-1) unstable; urgency=low

  * Support for kernel up to 5.9
  * usdt: add uprobe refcnt support
  * use newer llvm/clang versions in debian packaging if possible
  * add bpf iterator C++ support
  * new bcc tools: tcprtt, netqtop, swapin, tcpsynbl, threadsnoop
  * tcpconnect: add DNS correlation to connect tracking
  * new libbpf-tools: llcstat, numamove, runqlen, runqlat, softirgs, hardirqs
  * doc update, bug fixes and some additional arguments for tools

 -- Yonghong Song <<EMAIL>>  Thu, 29 Oct 2020 17:00:00 +0000

bcc (0.16.0-1) unstable; urgency=low

  * Support for kernel up to 5.8
  * trace.py: support kprobe/uprobe func offset
  * support raw perf config for perf_event_open in python
  * add BPFQueueStackTable support
  * added Ringbuf support support
  * libbpf-tools: readahead, biosnoop, bitesize, tcpconnlat, biopattern, biostacks
  * bug fixes and some additional arguments for tools

 -- Yonghong Song <<EMAIL>>  Sat, 22 Aug 2020 17:00:00 +0000

bcc (0.15.0-1) unstable; urgency=low

  * Support for kernel up to 5.7
  * new tools: funcinterval.py, dirtop.py
  * support lsm bpf programs
  * support multiple pid/tids for offwaketime
  * usdt: add helpers to set semaphore values
  * turn off x86 jump table optimization during jit compilation
  * add support to use bpf_probe_read[_str_}{_user,kernel} in all bpf
  *    programs, fail back to old bpf_probe_read[_str] for old kernels
  * tools: add filtering by mount namespace
  * libbpf-tools: cpudist, syscount, execsnoop, vfsstat
  * lots of bug fixes and a few additional arguments for tools

 -- Yonghong Song <<EMAIL>>  Mon, 19 Jun 2020 17:00:00 +0000

bcc (0.14.0-1) unstable; urgency=low

  * Support for kernel up to 5.6
  * new tools: biolatpcts.py
  * libbpf-tools: tools based on CORE and libbpf library directly
  * add --cgroupmap to various tools, filtering based cgroup
  * support kfunc (faster kprobe) for vfsstat, klockstat and opensnoop
  * lots of bug fixes and a few additional arguments for tools

 -- Yonghong Song <<EMAIL>>  Mon, 20 Apr 2020 17:00:00 +0000

bcc (0.13.0-1) unstable; urgency=low

  * Support for kernel up to 5.5
  * bindsnoop tool to track tcp/udp bind information
  * added compile-once run-everywhere based libbpf-tools, currently
    only runqslower is implemented.
  * new map support: sockhash, sockmap, sk_storage, cgroup_storage
  * enable to run github actions on the diff
  * cgroupmap based cgroup filtering for opensnoop, execsnoop and bindsnoop.
  * lots of bug fixes.

 -- Yonghong Song <<EMAIL>>  Wed, 19 Feb 2020 17:00:00 +0000

bcc (0.12.0-1) unstable; urgency=low

  * Support for kernel up to 5.4
  * klockstat tool to track kernel mutex lock statistics
  * cmake option CMAKE_USE_LIBBPF_PACKAGE to build a bcc shared library
    linking with distro libbpf_static.a
  * new map.lookup_or_try_init() API to remove hidden return in
    map.lookup_or_init()
  * BPF_ARRAY_OF_MAPS and BPF_HASH_OF_MAPS support
  * support symbol offset for uprobe in both C++ and python API,
    kprobe already has the support
  * bug fixes for trace.py, tcpretrans.py, runqslower.py, etc.

 -- Yonghong Song <<EMAIL>>  Tue, 10 Dec 2019 17:00:00 +0000

bcc (0.11.0-1) unstable; urgency=low

  * Support for kernel up to 5.3
  * Corresponding libbpf submodule release is v0.0.5
  * Fix USDT issue with multi-threaded applications
  * Fixed the early return behavior of lookup_or_init
  * Support for nic hardware offload
  * Fixed and Enabled Travis CI
  * A lot of tools change with added new options, etc.

 -- Yonghong Song <<EMAIL>>  Tue, 03 Oct 2019 17:00:00 +0000

bcc (0.10.0-1) unstable; urgency=low

  * Support for kernel up to 5.1
  * corresponding libbpf submodule release is v0.0.3
  * support for reading kernel headers from /proc
  * libbpf.{a,so} renamed to libcc_bpf.{a,so}
  * new common options for some tools
  * new tool: drsnoop
  * s390 USDT support

 -- Brenden Blanco <<EMAIL>>  Tue, 28 May 2019 17:00:00 +0000

bcc (0.9.0-1) unstable; urgency=low

  * Adds support for BTF
  * Uses libbpf common library to wrap syscall API
  * Many bugfixes and new tools

 -- Brenden Blanco <<EMAIL>>  Thu, 07 Mar 2019 17:00:00 +0000

bcc (0.8.0-1) unstable; urgency=low

  * Support for kernel up to 5.0

 -- Brenden Blanco <<EMAIL>>  Fri, 11 Jan 2019 17:00:00 +0000

bcc (0.7.0-1) unstable; urgency=low

  * Support for kernel up to 4.18

 -- Brenden Blanco <<EMAIL>>  Tue, 04 Sep 2018 17:00:00 +0000

bcc (0.6.1-1) unstable; urgency=low

  * Build support for Fedora 28 and Ubuntu 18.04
  * Add option to change license
  * Optimizations for some uses of bpf_probe_reads

 -- Brenden Blanco <<EMAIL>>  Mon, 23 Jul 2018 17:00:00 +0000

bcc (0.6.0-1) unstable; urgency=low

  * Support for kernel up to 4.17
  * Many bugfixes
  * Many new tools
  * Improved python3 support

 -- Brenden Blanco <<EMAIL>>  Wed, 13 Jun 2018 17:00:00 +0000

bcc (0.5.0-1) unstable; urgency=low

  * Support for USDT in ARM64
  * Bugfixes for 4.14 in some tools
  * Fixes for smoke test failures
  * Runtime memory usage reductions

 -- Brenden Blanco <<EMAIL>>  Wed, 29 Nov 2017 17:00:00 +0000

bcc (0.4.0-1) unstable; urgency=low

  * Bugfixes
  * Support for kernel up to 4.14

 -- Brenden Blanco <<EMAIL>>  Fri, 20 Oct 2017 17:00:00 +0000

bcc (0.3.0-1) unstable; urgency=low

  * Many bugfixes
  * Many tools converted to perf ring buffer
  * New utilities in tools/
   * capable, cpuunclaimed, dbslower, dbstat, deadlock_detector, llcstat,
     mountsnoop, runqlen, slabratetop, syscount, tcplife, tcptop, ttysnoop,
     ucalls, uflow, ugc, uobjnew, ustat, uthreads
  * New C++ API
  * Support for kernel up to 4.10

 -- Brenden Blanco <<EMAIL>>  Thu, 09 Mar 2017 19:08:08 +0000

bcc (0.2.0-1) unstable; urgency=low

  * Add many new utilities in tools/
  * Support for USDT
  * Support for lua
  * Many utilities converted to perf ring buffer
  * Support for tracepoints

 -- Brenden Blanco <<EMAIL>>  Thu, 08 Sep 2016 17:05:28 -0700

bcc (0.1.8-1) unstable; urgency=low

  * Add many new utilities in tools/
   * wakeuptime, offwaketime, argdist, {xfs,zfs,ext4}{slower,dist}, others
  * Support for bpf_perf_event()
  * Support for public tables shared between programs
  * Support for up to 4.4 features
  * Remove external file dependencies from clang lib

 -- Brenden Blanco <<EMAIL>>  Mon, 23 Feb 2016 00:41:00 +0000

bcc (0.1.7-1) unstable; urgency=low

  * Tracing features and bugfixes
  * Built against LLVM 3.8 HEAD

 -- Brenden Blanco <<EMAIL>>  Mon, 12 Oct 2015 16:47:09 +0000

bcc (0.1.6-1) unstable; urgency=low

  * Stability fixes
  * Improvements to python API
  * Tracing features
  * Support for kernel 4.2 features

 -- Brenden Blanco <<EMAIL>>  Wed, 02 Sep 2015 16:23:19 +0000

bcc (0.1.5-1) unstable; urgency=low

  * Initial release

 -- Brenden Blanco <<EMAIL>>  Mon, 06 Jul 2015 18:04:28 +0000
