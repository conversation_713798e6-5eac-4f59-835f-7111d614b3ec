- 2019-12-06: [My learnings on Linux BPF container performance engineering](https://medium.com/@aimvec/my-learnings-on-linux-bpf-container-performance-engineering-3eb424b73d56)
- 2019-11-21: [Debugging network stalls on Kubernetes](https://github.blog/2019-11-21-debugging-network-stalls-on-kubernetes)
- 2019-11-12: [bcc-tools brings dynamic kernel tracing to Red Hat Enterprise Linux 8.1](https://www.redhat.com/en/blog/bcc-tools-brings-dynamic-kernel-tracing-red-hat-enterprise-linux-81)
- 2018-05-03: [Linux System Monitoring with eBPF](https://www.heinrichhartmann.com/pdf/Heinrich%20Hartmann%20-%20Linux%20System%20Monitoring%20with%20eBPF.pdf)
- 2018-02-22: [Some advanced BCC topics](https://lwn.net/Articles/747640)
- 2018-01-23: [BPFd: Running BCC tools remotely across systems and architectures](https://lwn.net/Articles/744522)
- 2017-12-22: [An introduction to the BPF Compiler Collection](https://lwn.net/Articles/742082)
- 2017-09-13: [Performance Analysis Superpowers with Linux BPF](https://www.slideshare.net/brendangregg/ossna-2017-performance-analysis-superpowers-with-linux-bpf)
- 2017-07-28: [Tracing a packet journey using Linux tracepoints, perf and eBPF](https://blog.yadutaf.fr/2017/07/28/tracing-a-packet-journey-using-linux-tracepoints-perf-ebpf/)
- 2017-07-13: [Performance Superpowers with Enhanced BPF](https://www.usenix.org/conference/atc17/program/presentation/gregg-superpowers)
- 2017-06-28: [The BSD Packet Filter](https://speakerdeck.com/tuxology/the-bsd-packet-filter)
- 2017-03-04: [Linux 4.x Tracing: Performance Analysis with bcc/BPF](https://www.slideshare.net/brendangregg/linux-4x-tracing-performance-analysis-with-bccbpf)
- 2017-02-05: [gobpf - utilizing eBPF from Go](https://fosdem.org/2017/schedule/event/go_bpf/attachments/slides/1681/export/events/attachments/go_bpf/slides/1681/gobpf_utilizing_eBPF_from_Go_FOSDEM_2017.pdf)
- 2017-01-31: [Golang bcc/BPF Function Tracing](http://www.brendangregg.com/blog/2017-01-31/golang-bcc-bpf-function-tracing.html)
- 2017-01-18: [BPF: Tracing and more](https://www.slideshare.net/brendangregg/bpf-tracing-and-more)
- 2016-12-09: [Linux 4.x Tracing Tools: Using BPF Superpowers](https://www.slideshare.net/brendangregg/linux-4x-tracing-tools-using-bpf-superpowers)
- 2016-11-30: [Introducing gobpf - Using eBPF from Go](https://kinvolk.io/blog/2016/11/ebpf-gobpf)
- 2016-11-30: [Linux bcc/BPF tcplife: TCP Lifespans](http://www.brendangregg.com/blog/2016-11-30/linux-bcc-tcplife.html)
- 2016-10-27: [DTrace for Linux 2016](http://www.brendangregg.com/blog/2016-10-27/dtrace-for-linux-2016.html)
- 2016-10-21: [Linux 4.9's Efficient BPF-based Profiler](http://www.brendangregg.com/blog/2016-10-21/linux-efficient-profiler.html)
- 2016-10-15: [Linux bcc tcptop](http://www.brendangregg.com/blog/2016-10-15/linux-bcc-tcptop.html)
- 2016-10-12: [Linux bcc/BPF Node.js USDT Tracing](http://www.brendangregg.com/blog/2016-10-12/linux-bcc-nodejs-usdt.html)
- 2016-10-08: [Linux bcc/BPF Run Queue (Scheduler) Latency](http://www.brendangregg.com/blog/2016-10-08/linux-bcc-runqlat.html)
- 2016-10-06: [Linux bcc ext4 Latency Tracing](http://www.brendangregg.com/blog/2016-10-06/linux-bcc-ext4dist-ext4slower.html)
- 2016-10-04: [Installing bcc to evaluate BPF and Postgres](https://www.gregburek.com/2016/10/04/installing-bcc-to-evaluate-bpf-and-postgres)
- 2016-10-04: [Linux MySQL Slow Query Tracing with bcc/BPF](http://www.brendangregg.com/blog/2016-10-04/linux-bcc-mysqld-qslower.html)
- 2016-10-01: [Linux bcc Tracing Security Capabilities](http://www.brendangregg.com/blog/2016-10-01/linux-bcc-security-capabilities.html)
- 2016-09-23: [BCC – Dynamic Tracing Tools for Linux Performance Monitoring, Networking and More](http://www.tecmint.com/bcc-best-linux-performance-monitoring-tools/)
- 2016-08-22: [BoF - What Can BPF Do For You?](https://events.static.linuxfound.org/sites/events/files/slides/iovisor-lc-bof-2016.pdf)
- 2016-07-03: [Linux debugging tools I love](https://jvns.ca/blog/2016/07/03/debugging-tools-i-love)
- 2016-06-14: [Ubuntu Xenial bcc/BPF](http://www.brendangregg.com/blog/2016-06-14/ubuntu-xenial-bcc-bpf.html)
- 2016-05-26: [Linux BPF/bcc for Oracle Tracing](https://db-blog.web.cern.ch/blog/luca-canali/2016-05-linux-bpfbcc-oracle-tracing)
- 2016-03-30: [How to turn any syscall into an event: Introducing eBPF Kernel probes](https://blog.yadutaf.fr/2016/03/30/turn-any-syscall-into-event-introducing-ebpf-kernel-probes)
- 2016-03-28: [Linux BPF/bcc Road Ahead, March 2016](http://www.brendangregg.com/blog/2016-03-28/linux-bpf-bcc-road-ahead-2016.html)
- 2016-03-05: [Linux BPF Superpowers](http://www.brendangregg.com/blog/2016-03-05/linux-bpf-superpowers.html)
- 2016-03-02: [Linux BPF Superpowers](https://www.slideshare.net/brendangregg/linux-bpf-superpowers)
- 2016-02-08: [Linux eBPF/bcc uprobes](http://www.brendangregg.com/blog/2016-02-08/linux-ebpf-bcc-uprobes.html)
- 2016-02-05: [Who is waking the waker? (Linux chain graph prototype)](http://www.brendangregg.com/blog/2016-02-05/ebpf-chaingraph-prototype.html)
- 2016-02-01: [Linux Wakeup and Off-Wake Profiling](http://www.brendangregg.com/blog/2016-02-01/linux-wakeup-offwake-profiling.html)
- 2016-01-20: [Linux eBPF Off-CPU Flame Graph](http://www.brendangregg.com/blog/2016-01-20/ebpf-offcpu-flame-graph.html)
- 2016-01-18: [Linux eBPF Stack Trace Hack](http://www.brendangregg.com/blog/2016-01-18/ebpf-stack-trace-hack.html)
- 2015-10-31: [tcpconnect and tcpaccept for Linux (bcc)](http://www.brendangregg.com/blog/2015-10-31/tcpconnect-tcpaccept-bcc.html)
- 2015-09-22: [bcc: Taming Linux 4.3+ Tracing Superpowers](http://www.brendangregg.com/blog/2015-09-22/bcc-linux-4.3-tracing.html)
